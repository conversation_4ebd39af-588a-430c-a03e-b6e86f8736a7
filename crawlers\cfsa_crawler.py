import requests
from bs4 import BeautifulSoup
import time
from urllib.parse import urljoin
import urllib3
import logging
from crawlers.base_crawler import BaseCrawler
from models.data_general import DataGeneral

class CFSACrawler(BaseCrawler):
    """
    中国食品安全风险评估中心爬虫
    """
    
    def __init__(self, task_id, batch_id=None):
        """
        初始化爬虫
        
        Args:
            task_id (int): 任务ID
            batch_id (str, optional): 批次ID，如果不提供则自动生成
        """
        super().__init__(task_id, batch_id)
        self.base_url = "https://www.cfsa.net.cn"
        self.target_url = "https://www.cfsa.net.cn/fxpg/fxpgbg/wsw/index.shtml"
        self.source = "中国食品安全风险评估中心"
        
        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 更新会话头
        self.session.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def _parse_risk_assessment_categories(self, html_content):
        """
        解析风险评估报告分类信息
        
        Args:
            html_content (str): HTML内容
            
        Returns:
            list: 分类信息列表
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        categories = []
        
        # 查找title="风险评估报告"的a标签
        risk_assessment_links = soup.find_all('a', title='风险评估报告')
        
        for link in risk_assessment_links:
            # 找到包含该链接的父级li元素
            parent_li = link.find_parent('li')
            if parent_li:
                # 查找该li下的ul.left-secondnav
                second_nav = parent_li.find('ul', class_='left-secondnav')
                if second_nav:
                    # 提取所有子分类
                    sub_categories = second_nav.find_all('li')
                    for sub_li in sub_categories:
                        sub_link = sub_li.find('a')
                        if sub_link:
                            category_info = {
                                'title': sub_link.get('title', '').strip(),
                                'text': sub_link.get_text().strip(),
                                'href': sub_link.get('href', '').strip(),
                                'full_url': urljoin(self.base_url, sub_link.get('href', '')) if sub_link.get('href') else '',
                                'class': sub_link.get('class', [])
                            }
                            categories.append(category_info)
                    break  # 找到第一个匹配的就退出
        
        return categories
    
    def _parse_report_list(self, html_content):
        """
        解析报告列表页面的内容
        
        Args:
            html_content (str): HTML内容
            
        Returns:
            list: 报告列表
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        reports = []
        
        # 查找ul.news-list
        news_list = soup.find('ul', class_='news-list')
        if news_list:
            # 提取所有li元素
            list_items = news_list.find_all('li')
            for li in list_items:
                # 查找a标签和日期span
                link = li.find('a')
                date_span = li.find('span')
                
                if link and date_span:
                    report_info = {
                        'title': link.get('title', '').strip() or link.get_text().strip(),
                        'href': link.get('href', '').strip(),
                        'full_url': urljoin(self.base_url, link.get('href', '')) if link.get('href') else '',
                        'date': date_span.get_text().strip()
                    }
                    reports.append(report_info)
        
        return reports
    
    def _crawl(self):
        """
        执行抓取逻辑
        
        Returns:
            bool: 抓取成功返回True，否则返回False
        """
        try:
            self.logger.info("开始抓取中国食品安全风险评估中心数据")
            
            # 获取页面内容
            response = self.make_request(self.target_url, timeout=30, verify=False)
            if not response:
                self.logger.error("请求主页面失败")
                return False
                
            html_content = response.text
            
            # 解析分类信息
            categories = self._parse_risk_assessment_categories(html_content)
            if not categories:
                self.logger.error("未找到风险评估报告分类")
                return False
                
            self.logger.info(f"找到 {len(categories)} 个风险评估报告分类")
            
            all_reports = []
            
            # 遍历分类，抓取报告
            for category in categories:
                self.logger.info(f"正在抓取分类: {category['title']}")
                
                # 获取分类页面内容
                category_response = self.make_request(category['full_url'], timeout=30, verify=False)
                if not category_response:
                    self.logger.warning(f"请求分类页面失败: {category['title']}")
                    continue
                    
                # 解析报告列表
                reports = self._parse_report_list(category_response.text)
                self.logger.info(f"在分类 {category['title']} 中找到 {len(reports)} 个报告")
                
                # 处理报告数据
                for report in reports:
                    report_dict = {
                        'title': report['title'],
                        'summary': "",
                        'publish_date': report['date'],
                        'category': "",
                        'risk_category': category['title'],
                        'risk_factor': category['title'],
                        'source': self.source,
                        'source_url': report['full_url'],
                        'country': "中国",
                        'origin_country': "中国"
                    }
                    all_reports.append(report_dict)
                
                # 避免请求过快
                time.sleep(1)
            
            # 保存结果
            if all_reports:
                self.save_records(all_reports, DataGeneral)
                self.logger.info(f"成功抓取 {len(all_reports)} 条中国食品安全风险评估中心数据")
                return True
            else:
                self.logger.warning("未抓取到任何中国食品安全风险评估中心数据")
                return False
                
        except Exception as e:
            self.logger.error(f"抓取中国食品安全风险评估中心数据失败: {e}")
            return False
